package org.jeecg.modules.lp;

import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.lp.activityuserinfo.entity.ActivityUserInfo;
import org.jeecg.modules.lp.activityuserinfo.service.IActivityUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/8/4
 */
@Slf4j
@Api(tags = "公益活动-公共接口")
@RestController
@RequestMapping("/lpactivity/public")
public class ActivityPublicController {

    @Autowired
    private IActivityUserInfoService activityUserInfoService;

    /**
     * 活动一览列表
     */
    @AutoLog(value = "公益活动-公共接口-按月分页列表查询")
    @ApiOperation(value = "公益活动-公共接口-按月分页列表查询", notes = "公益活动-公共接口-按月分页列表查询")
    @RequestMapping("/listByMonth")
    public Page<ActivityUserInfo> listByMonth(@RequestParam(name = "page", defaultValue = "1") Integer page,
                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                       @RequestParam(name = "month", required = false) String month,
                       @RequestParam(name = "xm", required = false) String xm,
                       @RequestParam(name = "jzjg", required = false) String jzjg,
                       @RequestParam(name = "status", required = false) String status
                       ) {
        Page<ActivityUserInfo> pageInfo = new Page<>(page, pageSize);
        QueryWrapper<ActivityUserInfo> queryWrapper = new QueryWrapper<>();

        if (month != null && !month.trim().isEmpty()) {
            queryWrapper.eq("month", month);
        }
        if (xm != null && !xm.trim().isEmpty()) {
            queryWrapper.like("xm", xm);
        }
        if (jzjg != null && !jzjg.trim().isEmpty()) {
            queryWrapper.eq("jzjg", jzjg);
        }
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq("status", status);
        }

        activityUserInfoService.page(pageInfo, queryWrapper);
        return pageInfo;
    }

}

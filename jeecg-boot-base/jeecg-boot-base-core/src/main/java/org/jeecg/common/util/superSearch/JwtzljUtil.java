package org.jeecg.common.util.superSearch;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;

import java.util.Date;

/**
 * 浙里矫工具类
 */
public class JwtzljUtil {

    //
    public static final long EXPIRE_TIME = 1 * 5 * 60 * 1000;



    /**
     * 校验token是否正确
     *
     * @param token  密钥
     * @param secret 用户的密码
     * @return 真实token
     */
    public static boolean verify(String token, String username, String secret) {
        try {
            String userId = getUserId(token);

            // 根据密码生成JWT效验器
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).withClaim("username", username).build();
            // 效验TOKEN
            DecodedJWT jwt = verifier.verify(token);
            return true;
        } catch (Exception exception) {
            return false;
        }
    }



    /**
     * 获得token中的信息无需secret解密也能获得
     *
     * @return token中包含的用户名
     */
    public static String getUserId(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("id").asString();
        } catch (JWTDecodeException e) {
            return null;
        }
    }


    /**
     * 生成签名,5min后过期
     * 浙里矫
     * @param id 用户id
     * @param key 秘钥
     * @return 加密的token
     */
    public static String zljsign(String id, String key) {
        Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
        Algorithm algorithm = Algorithm.HMAC256(key);
        // 附带username信息
        return JWT.create().withClaim("id", id).withExpiresAt(date).sign(algorithm);
    }


    public static void main(String[] args) {

        String zljsign = zljsign("6666666R", "222");
        String userId = getUserId(zljsign);
        System.out.println(userId);

    }
}

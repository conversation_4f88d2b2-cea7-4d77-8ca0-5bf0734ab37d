package org.jeecg.common.constant;

/**
 * @author: huangxutao
 * @date: 2019-06-14
 * @description: 缓存常量
 */
public interface CacheConstant {

    /**
     * 字典信息缓存
     */
    public static final String SYS_DICT_CACHE = "sys:cache:dict";

    public static final String SYS_COURSEWARE_LABEL_ID = "SYS:COURSEWARE:LABEL:ID";
    /**
     * 用户数据权限缓存
     */
    public static final String SYS_PERMISSION_USER_DEPART_LIST = "SYS:PERMISSION:USER_DEPART_LIST";
    public static final String SYS_PERMISSION_CORR_DEPART_LIST = "SYS:PERMISSION:CORR_DEPART_LIST";
    public static final String SYS_PERMISSION_DEPART_LIST_BY_ID = "SYS:PERMISSION:DEPART_LIST_BY_ID";
    public static final String SYS_PERMISSION_USER_DEPART = "SYS:PERMISSION:USER_DEPART";
    public static final String SYS_PERMISSION_USER_SP_DEPART = "SYS:PERMISSION:USER_SP_DEPART";
    /**
     * 表字典信息缓存
     */
    public static final String SYS_DICT_TABLE_CACHE = "sys:cache:dictTable";
    public static final String SYS_DICT_TABLE_BY_KEYS_CACHE = SYS_DICT_TABLE_CACHE + "ByKeys";

    /**
     * 数据权限配置缓存
     */
    public static final String SYS_DATA_PERMISSIONS_CACHE = "sys:cache:permission:datarules";

    /**
     * 缓存用户信息
     */
    public static final String SYS_USERS_CACHE = "sys:cache:user";

    /**
     * 全部部门信息缓存
     */
    public static final String SYS_DEPARTS_CACHE = "sys:cache:depart:alldata";


    /**
     * 全部部门ids缓存
     */
    public static final String SYS_DEPART_IDS_CACHE = "sys:cache:depart:allids";


    /**
     * 测试缓存key
     */
    public static final String TEST_DEMO_CACHE = "test:demo";

    /**
     * 字典信息缓存
     */
    public static final String SYS_DYNAMICDB_CACHE = "sys:cache:dbconnect:dynamic:";

    /**
     * gateway路由缓存
     */
    public static final String GATEWAY_ROUTES = "geteway_routes";


    /**
     * gateway路由 reload key
     */
    public static final String ROUTE_JVM_RELOAD_TOPIC = "gateway_jvm_route_reload_topic";

    /**
     * TODO 冗余代码 待删除
     * 插件商城排行榜
     */
    public static final String PLUGIN_MALL_RANKING = "pluginMall::rankingList";
    /**
     * TODO 冗余代码 待删除
     * 插件商城排行榜
     */
    public static final String PLUGIN_MALL_PAGE_LIST = "pluginMall::queryPageList";
    /**
     * 矫务公开缓存用户id
     */
    public static final String DISCLOSURE_USER_ID = "disclosure:user:id";
    public static final String SYS_LOG = "sys:log";
}

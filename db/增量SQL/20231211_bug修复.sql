
ALTER TABLE month_records_need_study ADD score int default 0 COMMENT '积分' AFTER is_complete;

UPDATE  month_records_need_study m SET m.score = (SELECT r.score FROM records r
      WHERE m.year = 2023 AND m.month = 12 and r.year = 2023 AND r.month = 12 AND r.user_id = m.user_id AND r.courseware_id = m.course_id AND m.del_fag=0 AND r.is_del = 0 AND r.is_max = '1' LIMIT 1);

-- 学习记录(只存储当月，用于关联查询列表课件当月是否已完成
CREATE TABLE records_temp (
     id varchar(128) NOT NULL COMMENT 'id',
     courseware_id varchar(128) DEFAULT NULL COMMENT '课件id',
     user_id varchar(128) DEFAULT NULL COMMENT '用户id',
     user_deptid varchar(128) DEFAULT NULL COMMENT '用户部门',
     score int(32) DEFAULT NULL COMMENT '积分',
     year int(32) DEFAULT NULL COMMENT '学习年份',
     month int(32) DEFAULT NULL COMMENT '学习月份',
     day int(32) DEFAULT NULL COMMENT '学习日期',
     type varchar(1) DEFAULT NULL,
     is_max varchar(1) DEFAULT NULL,
     create_time datetime DEFAULT NULL COMMENT '创建时间',
     PRIMARY KEY (id)
)
COMMENT='学习记录临时表(只存近几个月)';
ALTER TABLE records_temp
    ADD INDEX `courseware_id` (courseware_id);

ALTER TABLE records_temp
    ADD INDEX `month` (month);

ALTER TABLE records_temp
    ADD INDEX `user_id` (user_id);

ALTER TABLE records_temp
    ADD INDEX `year` (year);

-- 定时任务清除records_temp数据
CREATE EVENT records_temp_delete
ON SCHEDULE
    EVERY 1 MONTH
    STARTS '2024-01-01 00:00:00'
DO
BEGIN
DELETE FROM  records_temp WHERE create_time < DATE_SUB(NOW(), INTERVAL 2 MONTH);
END;


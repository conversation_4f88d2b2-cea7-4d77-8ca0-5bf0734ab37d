-- 优化问卷统计查询性能的索引建议
-- 作者：System
-- 日期：2025-01-24
-- 用途：为 getLatestDataStatistics 接口优化数据库索引

-- 1. js_questionnaire_task_record 表索引优化
-- 为最新记录查询优化
ALTER TABLE js_questionnaire_task_record 
ADD INDEX idx_info_status (info_id, status);

-- 为任务信息关联查询优化
ALTER TABLE js_questionnaire_task_record 
ADD INDEX idx_task_info_status (task_info_id, status);

-- 2. js_questionnaire_task_info 表索引优化
-- 为问卷类型和时间查询优化
ALTER TABLE js_questionnaire_task_info 
ADD INDEX idx_survey_start_time (survey_id, start_time);

-- 3. js_questionnaire_answers 表索引优化
-- 为答案查询优化
ALTER TABLE js_questionnaire_answers 
ADD INDEX idx_task_info_answer (task_info_id, info_id, answer_id);

-- 为问题关联查询优化
ALTER TABLE js_questionnaire_answers 
ADD INDEX idx_question_answer (question_id, answer_id);

-- 4. correction_object_basic 表索引优化
-- 为矫正对象状态查询优化
ALTER TABLE correction_object_basic 
ADD INDEX idx_jzjg_status_del (jzjg, zhuangtai, del_flag);

-- 5. sys_depart 表索引优化（如果不存在的话）
-- 检查是否已有主键索引，如果没有则添加
-- ALTER TABLE sys_depart ADD INDEX idx_id (id);

-- 6. exam_question1 表索引优化
-- 为问题类型查询优化
ALTER TABLE exam_question1 
ADD INDEX idx_type_id (type_id);

-- 7. exam_question_item1 表索引优化
-- 为选项查询优化
ALTER TABLE exam_question_item1 
ADD INDEX idx_id (id);

-- 8. correction_object_item 表索引优化
-- 为户籍信息查询优化
ALTER TABLE correction_object_item 
ADD INDEX idx_id_hjszxq (id, hjszxq_name);

-- 查看索引使用情况的查询语句（用于性能监控）
-- EXPLAIN SELECT ... 可以用来分析查询执行计划

-- 注意事项：
-- 1. 在生产环境执行前请先在测试环境验证
-- 2. 添加索引会占用额外的存储空间
-- 3. 索引会影响INSERT/UPDATE/DELETE的性能，但会显著提升SELECT性能
-- 4. 建议在业务低峰期执行索引创建操作
-- 5. 执行前请备份相关表数据


ALTER TABLE `correction_object_basic` ADD COLUMN `exemption_activity_start_time` datetime NULL DEFAULT NULL COMMENT '免除公益活动开始时间' AFTER `exemption_of_study_end_time`;

ALTER TABLE `correction_object_basic` ADD COLUMN `exemption_activity_end_time` datetime NULL DEFAULT NULL COMMENT '免除公益活动结束时间' AFTER `exemption_activity_start_time`;

ALTER TABLE `correction_object_basic` ADD COLUMN `activity_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '免除公益活动原因' AFTER `reason`;
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777546032692195329', '1777545760389591041', '免除公益活动', '/activity/activityimmunity/Immunity', 'activity/activityimmunity/Immunity', NULL, NULL, 1, NULL, '1', 1.00, 0, NULL, 1, 1, 0, 0, NULL, 'admin', '2024-04-09 11:56:14', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777545760389591041', '', '免除公益活动', '/exemptionActivity', 'layouts/RouteView', NULL, NULL, 0, NULL, '1', 2.00, 0, 'trademark', 1, 0, 0, 0, NULL, 'admin', '2024-04-09 11:55:09', 'admin', '2024-04-09 13:30:31', 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777270397365161986', '1777235258878480386', '活动申诉', '/activity/activityuserdetail', 'activity/activityuserdetail/ActivityUserDetailList', NULL, NULL, 1, NULL, '1', 4.00, 0, NULL, 1, 1, 0, 0, NULL, 'admin', '2024-04-08 17:40:57', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777241632647696386', '1777241347619573761', '重大事项汇报', '/activity/majorissuereport', 'activity/majorissuereport/MajorIssueReportList', NULL, NULL, 1, NULL, '1', 1.00, 0, NULL, 1, 1, 0, 0, NULL, 'admin', '2024-04-08 15:46:39', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777241347619573761', '', '重大事项汇报', '/majorissuereport', 'layouts/RouteView', NULL, NULL, 0, NULL, '1', 2.00, 0, 'schedule', 1, 0, 0, 0, NULL, 'admin', '2024-04-08 15:45:31', 'admin', '2024-04-08 15:45:58', 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777241100839309313', '1777235258878480386', '预警处置', '/activity/activitywarninglog', 'activity/activitywarninglog/ActivityWarningLogList', NULL, NULL, 1, NULL, '1', 5.00, 0, NULL, 1, 1, 0, 0, NULL, 'admin', '2024-04-08 15:44:33', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777240588530241537', '1777235258878480386', '活动统计', '/activity/activitystatistic', 'activity/activitystatistic/ActivityStatisticList', NULL, NULL, 1, NULL, '1', 4.00, 0, NULL, 1, 1, 0, 0, NULL, 'admin', '2024-04-08 15:42:30', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777237135489781762', '1777235258878480386', '活动管理', '/activity/activitymanage', 'activity/activitymanage/ActivityManageList', NULL, NULL, 1, NULL, '1', 3.00, 0, NULL, 1, 1, 0, 0, NULL, 'admin', '2024-04-08 15:28:47', 'admin', '2024-04-08 15:35:02', 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777236856757309442', '1777235258878480386', '劳动基地', '/activity/activitybase', 'activity/activitybase/ActivityBaseList', NULL, NULL, 1, NULL, '1', 2.00, 0, NULL, 1, 1, 0, 0, NULL, 'admin', '2024-04-08 15:27:41', 'admin', '2024-04-08 15:34:10', 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777236601420664834', '1777235258878480386', '活动一览', '/activity/activityuserinfo', 'activity/activityuserinfo/ActivityUserInfoList', NULL, NULL, 1, NULL, '1', 1.00, 0, NULL, 1, 1, 0, 0, NULL, 'admin', '2024-04-08 15:26:40', 'admin', '2024-04-08 15:35:26', 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1777235258878480386', NULL, '公益劳动', '/activity', 'layouts/RouteView', NULL, NULL, 0, NULL, '1', 1.00, 0, 'file-word', 1, 0, 0, 0, NULL, 'admin', '2024-04-08 15:21:20', NULL, NULL, 0, 0, '1', 0);


INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `exact`) VALUES ('1781219093546803201', '训诫依据', 'xunjieyiju', '', 0, 'admin', '2024-04-19 15:11:40', NULL, NULL, 0, 0);
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `exact`) VALUES ('1781156394578272257', '基地类别', 'base_type', '', 0, 'admin', '2024-04-19 11:02:31', NULL, NULL, 0, 0);
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `exact`) VALUES ('1777870852067520513', '公益活动状态', 'activity_status', '', 0, 'admin', '2024-04-10 09:26:57', NULL, NULL, 0, 0);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1781156578305564673', '1781156394578272257', '教育、公益活动', '3', '', 3, 1, 'admin', '2024-04-19 11:03:15', NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1781156514019467266', '1781156394578272257', '公益活动', '2', '', 2, 1, 'admin', '2024-04-19 11:03:00', NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1781156446826717185', '1781156394578272257', '教育', '1', '', 1, 1, 'admin', '2024-04-19 11:02:44', NULL, NULL);

INSERT INTO `correct_lp`.`sys_role` (`id`, `role_name`, `role_code`, `description`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('GZTZTXR', '工作通知提醒人', 'GZTZTXR', NULL, 'admin', '2024-04-22 18:42:40', NULL, NULL);

CREATE TABLE `activity_base` (
                                 `id` varchar(64) NOT NULL COMMENT 'id',
                                 `base_name` varchar(255) DEFAULT NULL COMMENT '劳动基地名称',
                                 `jzjg` varchar(64) DEFAULT NULL COMMENT '矫正单位id',
                                 `jzjg_name` varchar(64) DEFAULT NULL COMMENT '矫正单位',
                                 `address` varchar(255) DEFAULT NULL COMMENT '地址',
                                 `event_feature` varchar(512) DEFAULT NULL COMMENT '活动特色',
                                 `workdays` varchar(100) DEFAULT NULL COMMENT '工作日',
                                 `open_time` varchar(10) DEFAULT NULL COMMENT '开放时间-开始',
                                 `close_time` varchar(10) DEFAULT NULL COMMENT '开放时间-结束',
                                 `longitude` varchar(100) DEFAULT NULL COMMENT '经度',
                                 `latitude` varchar(100) DEFAULT NULL COMMENT '纬度',
                                 `base_type` varchar(10) DEFAULT NULL COMMENT '基地类别',
                                 `supporting_facility` varchar(255) DEFAULT NULL COMMENT '配套设施',
                                 `max_person` tinyint(2) DEFAULT NULL COMMENT '可容纳人数',
                                 `land_area` varchar(10) DEFAULT NULL COMMENT '占地面积',
                                 `person_charge` varchar(50) DEFAULT NULL COMMENT '负责人',
                                 `telephone` varchar(20) DEFAULT NULL COMMENT '联系方式',
                                 `picture_ids` varchar(1024) DEFAULT NULL COMMENT '基地图片id',
                                 `remark` varchar(512) DEFAULT NULL COMMENT '备注',
                                 `launch_number` int(3) DEFAULT NULL COMMENT '开展次数',
                                 `total_participate` int(5) DEFAULT NULL COMMENT '参加总人数',
                                 `status` varchar(1) DEFAULT NULL COMMENT '状态（Y-启用,N-禁用）',
                                 `del_flag` tinyint(1) DEFAULT NULL COMMENT '是否删除（0-未删除；1-已删除）',
                                 `create_time` datetime DEFAULT NULL,
                                 `update_time` datetime DEFAULT NULL,
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公益活动-劳动基地';

CREATE TABLE `activity_manage` (
                                   `id` varchar(64) NOT NULL COMMENT 'id',
                                   `jzjg` varchar(64) DEFAULT NULL COMMENT '矫正单位id',
                                   `jzjg_name` varchar(64) DEFAULT NULL COMMENT '矫正单位',
                                   `activiti_name` varchar(255) DEFAULT NULL COMMENT '活动名称',
                                   `activiti_type` varchar(1) DEFAULT NULL COMMENT '公益活动形式',
                                   `person_number` int(4) DEFAULT NULL COMMENT '活动人数',
                                   `act_person_number` int(4) DEFAULT NULL COMMENT '实际参与人数',
                                   `begin_time` datetime DEFAULT NULL COMMENT '活动开始时间',
                                   `end_time` datetime DEFAULT NULL COMMENT '活动结束时间',
                                   `longitude` varchar(64) DEFAULT NULL COMMENT '经度',
                                   `latitude` varchar(64) DEFAULT NULL COMMENT '维度',
                                   `base_id` varchar(64) DEFAULT NULL COMMENT '劳动基地id',
                                   `address` varchar(255) DEFAULT NULL COMMENT '活动地点',
                                   `allow_radius` varchar(255) DEFAULT NULL COMMENT '允许签到半径',
                                   `activity_duration` tinyint(2) DEFAULT NULL COMMENT '活动时长',
                                   `description` varchar(512) DEFAULT NULL COMMENT '活动内容简述',
                                   `activiti_content` varchar(1) DEFAULT NULL COMMENT '活动内容',
                                   `activiti_belong` varchar(1) DEFAULT NULL COMMENT '活动类型',
                                   `person_charge` varchar(100) DEFAULT NULL COMMENT '负责人',
                                   `telephone` varchar(20) DEFAULT NULL COMMENT '联系电话',
                                   `choose_person` tinyint(1) DEFAULT NULL COMMENT '是否选择矫正对象（0-否，1-是））',
                                   `jzdx_ids` longtext COMMENT '矫正对象id集合',
                                   `create_type` tinyint(1) unsigned zerofill DEFAULT '0' COMMENT '创建类型（1-手动添加；0-自动生成）',
                                   `status` varchar(1) DEFAULT NULL COMMENT '状态',
                                   `create_time` datetime DEFAULT NULL COMMENT '发布时间',
                                   `create_by` varchar(255) DEFAULT NULL COMMENT '发布人员',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公益活动-活动管理';

CREATE TABLE `activity_statistic` (
                                      `id` varchar(64) NOT NULL COMMENT 'id',
                                      `month` varchar(10) DEFAULT NULL COMMENT '月份',
                                      `jzjg` varchar(64) DEFAULT NULL COMMENT '矫正单位id',
                                      `jzjg_name` varchar(64) DEFAULT NULL COMMENT '矫正单位',
                                      `total_person` tinyint(3) DEFAULT NULL COMMENT '参加公益活动总人数',
                                      `finish_person` tinyint(3) DEFAULT NULL COMMENT '完成公益活动人数',
                                      `unfinish_person` tinyint(3) DEFAULT NULL COMMENT '未完成公益活动人数',
                                      `finish_rate` decimal(4,2) DEFAULT NULL COMMENT '完成率',
                                      `free_person` tinyint(3) DEFAULT NULL COMMENT '免除公益活动人数',
                                      `create_time` datetime DEFAULT NULL,
                                      `create_by` varchar(255) DEFAULT NULL,
                                      `update_time` datetime DEFAULT NULL,
                                      `update_by` varchar(255) DEFAULT NULL,
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公益活动-活动统计';

CREATE TABLE `activity_user_detail` (
                                        `id` varchar(64) NOT NULL COMMENT 'id',
                                        `jzdx_id` varchar(64) DEFAULT NULL COMMENT '矫正对象id',
                                        `xm` varchar(20) DEFAULT NULL COMMENT '姓名',
                                        `grlxdh` varchar(20) DEFAULT NULL COMMENT '个人联系电话',
                                        `jzjg` varchar(50) DEFAULT NULL COMMENT '矫正单位id',
                                        `jzjg_name` varchar(50) DEFAULT NULL COMMENT '矫正单位',
                                        `base_name` varchar(255) DEFAULT NULL COMMENT '劳动基地名称',
                                        `activiti_manage_id` varchar(64) DEFAULT NULL COMMENT '公益活动id',
                                        `registration_time` datetime DEFAULT NULL COMMENT '报名时间',
                                        `sign_in_time` datetime DEFAULT NULL COMMENT '签到时间',
                                        `sign_in_address` varchar(255) DEFAULT NULL COMMENT '签到地点',
                                        `sign_up_time` datetime DEFAULT NULL COMMENT '签退时间',
                                        `sign_up_address` varchar(255) DEFAULT NULL COMMENT '签退地点',
                                        `duration` varchar(255) DEFAULT NULL COMMENT '时长',
                                        `event_reflection` varchar(255) DEFAULT NULL COMMENT '活动感想',
                                        `event_score` tinyint(1) DEFAULT NULL COMMENT '评分',
                                        `work_evaluation` varchar(1) DEFAULT NULL COMMENT '负责人评价（1-合格；2-不合格）',
                                        `work_content` varchar(512) DEFAULT NULL COMMENT '负责人评价内容',
                                        `work_user_id` varchar(64) DEFAULT NULL COMMENT '负责人id',
                                        `work_user_name` varchar(20) DEFAULT NULL COMMENT '负责人姓名',
                                        `work_user_phone` varchar(20) DEFAULT NULL COMMENT '负责人电话',
                                        `complaint_description` varchar(512) DEFAULT NULL COMMENT '申诉说明',
                                        `attachment_id` varchar(1024) DEFAULT NULL COMMENT '附件id',
                                        `processing_result` varchar(2) DEFAULT NULL COMMENT '处理结果（Y-申诉成功；N-申诉失败）',
                                        `processing_instruction` varchar(512) DEFAULT NULL COMMENT '处理说明',
                                        `status` varchar(1) DEFAULT NULL COMMENT '状态（1-已报名；2-已签到；3-已签退；4-已评价；5-已申诉；6申诉成功）',
                                        `base_id` varchar(64) DEFAULT NULL COMMENT '基地id',
                                        `activiti_type` varchar(1) DEFAULT NULL COMMENT '公益活动形式',
                                        `activiti_content` varchar(1) DEFAULT NULL COMMENT '公益活动内容',
                                        `activiti_belong` varchar(1) DEFAULT NULL COMMENT '活动类型',
                                        `create_type` tinyint(1) DEFAULT '1' COMMENT '创建类型（1-活动报名；2-自主上报）',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `create_by` varchar(255) DEFAULT NULL,
                                        `update_time` datetime DEFAULT NULL,
                                        `update_by` varchar(255) DEFAULT NULL,
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公益活动-矫正对象参与记录';

CREATE TABLE `activity_user_info` (
                                      `id` varchar(64) NOT NULL COMMENT 'id',
                                      `jzdx_id` varchar(64) DEFAULT NULL COMMENT '矫正对象id',
                                      `xm` varchar(20) DEFAULT NULL COMMENT '矫正对象姓名',
                                      `jzjg` varchar(64) DEFAULT NULL COMMENT '矫正单位id',
                                      `jzjg_name` varchar(64) DEFAULT NULL COMMENT '矫正单位',
                                      `participation_time` longtext COMMENT '参加时间',
                                      `month` varchar(10) DEFAULT NULL COMMENT '月份',
                                      `activiti_record_id` longtext COMMENT '活动记录id',
                                      `create_time` datetime DEFAULT NULL,
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公益活动-活动一览';

CREATE TABLE `activity_warning_log` (
                                        `id` varchar(64) NOT NULL COMMENT 'id',
                                        `jzdx_id` varchar(64) DEFAULT NULL COMMENT '矫正对象id',
                                        `month` varchar(20) DEFAULT NULL COMMENT '月份',
                                        `frequency` tinyint(3) DEFAULT NULL COMMENT '累计次数',
                                        `xm` varchar(20) DEFAULT NULL COMMENT '矫正对象姓名',
                                        `jzjg` varchar(64) DEFAULT NULL COMMENT '矫正单位id',
                                        `jzjg_name` varchar(64) DEFAULT NULL COMMENT '矫正单位',
                                        `warning_reason` varchar(255) DEFAULT NULL COMMENT '预警原因',
                                        `warning_time` datetime DEFAULT NULL COMMENT '预警时间',
                                        `status` varchar(1) DEFAULT NULL COMMENT '处理状态',
                                        `deal_result` varchar(1) DEFAULT NULL COMMENT '处置结果',
                                        `deal_base` varchar(512) DEFAULT NULL COMMENT '处置依据',
                                        `fact` varchar(512) DEFAULT NULL COMMENT '事实',
                                        `remark` varchar(512) DEFAULT NULL COMMENT '备注',
                                        `attachement_id` varchar(512) DEFAULT NULL COMMENT '笔录id',
                                        `update_user` varchar(255) DEFAULT NULL COMMENT '司法所处置人',
                                        `update_time` datetime DEFAULT NULL COMMENT '司法所处置时间',
                                        `create_time` datetime DEFAULT NULL,
                                        `create_by` varchar(255) DEFAULT NULL,
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公益活动-预警处置';

CREATE TABLE `major_issue_report` (
                                      `id` varchar(64) NOT NULL COMMENT 'id',
                                      `jzdx_id` varchar(64) DEFAULT NULL COMMENT '矫正对象id',
                                      `xm` varchar(20) DEFAULT NULL COMMENT '姓名',
                                      `jzjg` varchar(64) DEFAULT NULL COMMENT '矫正单位id',
                                      `jzjg_name` varchar(64) DEFAULT NULL COMMENT '矫正单位',
                                      `issue_type` varchar(1) DEFAULT NULL COMMENT '汇报事项',
                                      `issue_content` longtext COMMENT '具体说明',
                                      `original_address` varchar(255) DEFAULT NULL COMMENT '原地址',
                                      `original_address_detail` varchar(255) DEFAULT NULL COMMENT '原地址明细',
                                      `original_address_string` varchar(255) DEFAULT NULL COMMENT '原地址数组',
                                      `new_address` varchar(255) DEFAULT NULL COMMENT '新地址',
                                      `new_address_detail` varchar(255) DEFAULT NULL COMMENT '新地址明细',
                                      `new_address_string` varchar(255) DEFAULT NULL COMMENT '新地址数组',
                                      `create_time` datetime DEFAULT NULL COMMENT '汇报时间',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='重大事项汇报';

CREATE TABLE `sys_app` (
                           `app_code` varchar(20) NOT NULL COMMENT '应用编码',
                           `app_name` varchar(20) DEFAULT NULL COMMENT '应用名称',
                           `app_type` varchar(1) DEFAULT NULL COMMENT '类型（1-应用；2-菜单）',
                           `app_route` varchar(255) DEFAULT NULL COMMENT '路由',
                           PRIMARY KEY (`app_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='浙里矫正专区应用清单';

CREATE TABLE `sys_app_org` (
                               `id` varchar(50) NOT NULL COMMENT '标识',
                               `app_code` varchar(20) DEFAULT NULL COMMENT '应用编码',
                               `org_id` varchar(50) DEFAULT NULL COMMENT '机构id',
                               PRIMARY KEY (`id`) USING BTREE,
                               KEY `IDX_org_id` (`org_id`) USING BTREE,
                               KEY `IDX_app_code` (`app_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='浙里矫正专区权限管理';

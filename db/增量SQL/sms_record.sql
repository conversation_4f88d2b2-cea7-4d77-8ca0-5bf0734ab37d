-- 作者：System
-- 日期：2025-05-21
-- 用途：创建短信发送记录表

DROP TABLE IF EXISTS sms_record;

CREATE TABLE `sms_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `phone_numbers`   varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接收手机号',
    `content`         text COLLATE utf8mb4_unicode_ci        NOT NULL COMMENT '短信内容',
    `send_time`       datetime                               NOT NULL COMMENT '发送时间',
    `status`          tinyint(4) NOT NULL COMMENT '发送状态：0-发送中，1-发送成功，2-发送失败',
    `error_msg`       varchar(255) COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '错误信息',
    `sms_type`        tinyint(4) DEFAULT NULL COMMENT '短信类型：1-验证码，2-通知，3-营销',
    `template_id`     varchar(64) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '短信模板ID',
    `template_params` json                                            DEFAULT NULL COMMENT '短信模板参数',
    `provider`        varchar(32) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '短信服务提供商',
    `trace_id`        varchar(64) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '追踪ID',
    `create_time`     datetime                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY               `idx_phone_number` (`phone_numbers`),
    KEY               `idx_send_time` (`send_time`),
    KEY               `idx_send_status` (`status`),
    KEY               `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信发送记录表';

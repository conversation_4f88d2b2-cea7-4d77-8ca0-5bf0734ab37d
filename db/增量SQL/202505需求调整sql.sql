ALTER TABLE `major_issue_report` ADD COLUMN `status` VARCHAR(20) NULL COMMENT '处理状态' AFTER `create_time`;
ALTER TABLE `major_issue_report` ADD COLUMN `deal_user` VARCHAR(20) NULL COMMENT '处理人' AFTER `status`;
ALTER TABLE `major_issue_report` ADD COLUMN `deal_user_id` VARCHAR(64) NULL COMMENT '处理人id' AFTER `deal_user`;
ALTER TABLE `major_issue_report` ADD COLUMN `deal_time` DATETIME NULL COMMENT '处理时间' AFTER `deal_user_id`;

